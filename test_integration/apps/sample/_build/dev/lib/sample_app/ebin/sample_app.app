{application,sample_app,
    [{modules,
         ['Elixir.Drops.Relation.Loadable.Sample.Users.Views.Active',
          'Elixir.Drops.Relation.Loadable.Users',
          'Elixir.Ecto.Queryable.Sample.Users.Views.Active',
          'Elixir.Ecto.Queryable.Users',
          'Elixir.Enumerable.Sample.Users.Views.Active',
          'Elixir.Enumerable.Users','Elixir.Sample',
          'Elixir.Sample.Repo.Postgres','Elixir.Sample.Repo.Sqlite',
          'Elixir.Sample.Schemas.Active','Elixir.Sample.Schemas.User',
          'Elixir.Sample.Users.Views.Active',
          'Elixir.Sample.Users.Views.Active.QueryBuilder','Elixir.Users',
          'Elixir.Users.QueryBuilder']},
     {compile_env,
         [{sample_app,
              [drops,relation],
              {ok,[{repo,'Elixir.Sample.Repo.Postgres'},
                   {ecto_schema_namespace,['Elixir.Sample','Elixir.Schemas']},
                   {view_module,fun 'Elixir.Sample':view_module/1}]}}]},
     {optional_applications,[igniter]},
     {applications,
         [kernel,stdlib,elixir,logger,ecto,ecto_sqlite3,postgrex,
          drops_relation,jason,igniter]},
     {description,"sample_app"},
     {registered,[]},
     {vsn,"0.1.0"},
     {mod,{'Elixir.Sample',[]}}]}.
