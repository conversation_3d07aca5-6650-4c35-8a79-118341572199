{application,owl,
             [{modules,['Elixir.Inspect.Owl.Tag','Elixir.Owl',
                        'Elixir.Owl.Application','Elixir.Owl.BorderStyle',
                        'Elixir.Owl.Box','Elixir.Owl.Daemon',
                        'Elixir.Owl.Data','Elixir.Owl.Data.Sequence',
                        'Elixir.Owl.Data.Sequence.DSL','Elixir.Owl.IO',
                        'Elixir.Owl.Lines','Elixir.Owl.LiveScreen',
                        'Elixir.Owl.Palette','Elixir.Owl.ProgressBar',
                        'Elixir.Owl.Spinner','Elixir.Owl.System',
                        'Elixir.Owl.System.Helpers','Elixir.Owl.Table',
                        'Elixir.Owl.Tag','Elixir.Owl.Task',
                        'Elixir.Owl.TrueColor']},
              {optional_applications,[ucwidth]},
              {applications,[kernel,stdlib,elixir,logger,crypto,ucwidth]},
              {description,"owl"},
              {registered,[]},
              {vsn,"0.12.2"},
              {mod,{'Elixir.Owl.Application',[]}}]}.
