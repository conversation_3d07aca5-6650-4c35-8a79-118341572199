{<<"links">>,[{<<"GitHub">>,<<"https://github.com/elixir-mint/mint">>}]}.
{<<"name">>,<<"mint">>}.
{<<"version">>,<<"1.7.1">>}.
{<<"description">>,<<"Small and composable HTTP client.">>}.
{<<"elixir">>,<<"~> 1.12">>}.
{<<"app">>,<<"mint">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"castore">>},
   {<<"app">>,<<"castore">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.1.0 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"hpax">>},
   {<<"app">>,<<"hpax">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.1.1 or ~> 0.2.0 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/mint">>,<<"lib/mint/http1">>,
  <<"lib/mint/http1/response.ex">>,<<"lib/mint/http1/request.ex">>,
  <<"lib/mint/http1/parse.ex">>,<<"lib/mint/http2.ex">>,
  <<"lib/mint/tunnel_proxy.ex">>,<<"lib/mint/core">>,
  <<"lib/mint/core/util.ex">>,<<"lib/mint/core/transport">>,
  <<"lib/mint/core/transport/ssl.ex">>,<<"lib/mint/core/transport/tcp.ex">>,
  <<"lib/mint/core/transport.ex">>,<<"lib/mint/core/headers.ex">>,
  <<"lib/mint/core/conn.ex">>,<<"lib/mint/types.ex">>,
  <<"lib/mint/http_error.ex">>,<<"lib/mint/http2">>,
  <<"lib/mint/http2/frame.ex">>,<<"lib/mint/negotiate.ex">>,
  <<"lib/mint/http1.ex">>,<<"lib/mint/application.ex">>,
  <<"lib/mint/http.ex">>,<<"lib/mint/unsafe_proxy.ex">>,
  <<"lib/mint/transport_error.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE.txt">>,<<"CHANGELOG.md">>,<<"src">>,
  <<"src/mint_shims.erl">>]}.
{<<"build_tools">>,[<<"mix">>]}.
