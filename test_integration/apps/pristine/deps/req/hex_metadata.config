{<<"links">>,
 [{<<"Changelog">>,<<"https://hexdocs.pm/req/changelog.html">>},
  {<<"GitHub">>,<<"https://github.com/wojtekmach/req">>}]}.
{<<"name">>,<<"req">>}.
{<<"version">>,<<"0.5.15">>}.
{<<"description">>,<<"Req is a batteries-included HTTP client for Elixir.">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/req">>,<<"lib/req/response.ex">>,<<"lib/req/request.ex">>,
  <<"lib/req/test">>,<<"lib/req/test/adapter.ex">>,
  <<"lib/req/test/ownership.ex">>,<<"lib/req/test/ownership_error.ex">>,
  <<"lib/req/test.ex">>,<<"lib/req/steps.ex">>,<<"lib/req/finch.ex">>,
  <<"lib/req/http_error.ex">>,<<"lib/req/fields.ex">>,
  <<"lib/req/decompress_error.ex">>,<<"lib/req/response_async.ex">>,
  <<"lib/req/checksum_mismatch_error.ex">>,<<"lib/req/application.ex">>,
  <<"lib/req/utils.ex">>,<<"lib/req/archive_error.ex">>,
  <<"lib/req/too_many_redirects_error.ex">>,<<"lib/req/transport_error.ex">>,
  <<"lib/req.ex">>,<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"app">>,<<"req">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"finch">>},
   {<<"app">>,<<"finch">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.17">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"mime">>},
   {<<"app">>,<<"mime">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 2.0.6 or ~> 2.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"jason">>},
   {<<"app">>,<<"jason">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"nimble_csv">>},
   {<<"app">>,<<"nimble_csv">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"plug">>},
   {<<"app">>,<<"plug">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"brotli">>},
   {<<"app">>,<<"brotli">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.3.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"ezstd">>},
   {<<"app">>,<<"ezstd">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
