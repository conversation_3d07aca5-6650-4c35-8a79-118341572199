{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/ash-project/igniter">>},
  {<<"Discord">>,<<"https://discord.gg/HTHRaaVPUc">>},
  {<<"Website">>,<<"https://ash-hq.org">>},
  {<<"Forum">>,<<"https://elixirforum.com/c/ash-framework-forum/">>}]}.
{<<"name">>,<<"igniter">>}.
{<<"version">>,<<"0.6.25">>}.
{<<"description">>,<<"A code generation and project patching framework">>}.
{<<"elixir">>,<<"~> 1.15">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/igniter.ex">>,<<"lib/mix">>,<<"lib/mix/tasks">>,
  <<"lib/mix/tasks/igniter.setup.ex">>,
  <<"lib/mix/tasks/igniter.apply_upgrades.ex">>,
  <<"lib/mix/tasks/igniter.upgrade.ex">>,
  <<"lib/mix/tasks/igniter.move_files.ex">>,
  <<"lib/mix/tasks/igniter.update_gettext.ex">>,
  <<"lib/mix/tasks/igniter.remove.ex">>,<<"lib/mix/tasks/igniter.add.ex">>,
  <<"lib/mix/tasks/igniter.refactor.unless_to_if_not.ex">>,
  <<"lib/mix/tasks/igniter.refactor.rename_function.ex">>,
  <<"lib/mix/tasks/igniter.phx.install.ex">>,
  <<"lib/mix/tasks/igniter.add_extension.ex">>,
  <<"lib/mix/tasks/igniter.upgrade_igniter.ex">>,
  <<"lib/mix/tasks/igniter.gen.task.ex">>,
  <<"lib/mix/tasks/igniter.install.ex">>,<<"lib/mix/task.ex">>,
  <<"lib/mix/task">>,<<"lib/mix/task/info.ex">>,<<"lib/mix/task/args.ex">>,
  <<"lib/igniter">>,<<"lib/igniter/scribe.ex">>,
  <<"lib/igniter/copied_tasks.ex">>,<<"lib/igniter/rewrite">>,
  <<"lib/igniter/rewrite/dot_formatter_updater.ex">>,
  <<"lib/igniter/inflex.ex">>,<<"lib/igniter/util">>,
  <<"lib/igniter/util/io.ex">>,<<"lib/igniter/util/warning.ex">>,
  <<"lib/igniter/util/capture_server.ex">>,<<"lib/igniter/util/loading.ex">>,
  <<"lib/igniter/util/version.ex">>,<<"lib/igniter/util/install.ex">>,
  <<"lib/igniter/util/backward_compat.ex">>,<<"lib/igniter/util/info.ex">>,
  <<"lib/igniter/util/debug.ex">>,<<"lib/igniter/test.ex">>,
  <<"lib/igniter/phoenix">>,<<"lib/igniter/phoenix/single.ex">>,
  <<"lib/igniter/phoenix/generator.ex">>,<<"lib/igniter/code">>,
  <<"lib/igniter/code/tuple.ex">>,<<"lib/igniter/code/list.ex">>,
  <<"lib/igniter/code/string.ex">>,<<"lib/igniter/code/module.ex">>,
  <<"lib/igniter/code/function.ex">>,<<"lib/igniter/code/map.ex">>,
  <<"lib/igniter/code/keyword.ex">>,<<"lib/igniter/code/common.ex">>,
  <<"lib/igniter/extensions">>,<<"lib/igniter/extensions/phoenix.ex">>,
  <<"lib/igniter/libs">>,<<"lib/igniter/libs/ecto.ex">>,
  <<"lib/igniter/libs/phoenix.ex">>,<<"lib/igniter/libs/swoosh.ex">>,
  <<"lib/igniter/project">>,<<"lib/igniter/project/test.ex">>,
  <<"lib/igniter/project/deps.ex">>,<<"lib/igniter/project/formatter.ex">>,
  <<"lib/igniter/project/config.ex">>,
  <<"lib/igniter/project/igniter_config.ex">>,
  <<"lib/igniter/project/task_aliases.ex">>,
  <<"lib/igniter/project/module.ex">>,
  <<"lib/igniter/project/application.ex">>,
  <<"lib/igniter/project/mix_project.ex">>,<<"lib/igniter/refactors">>,
  <<"lib/igniter/refactors/rename.ex">>,<<"lib/igniter/refactors/elixir.ex">>,
  <<"lib/igniter/upgrades.ex">>,<<"lib/igniter/upgrades">>,
  <<"lib/igniter/upgrades/igniter.ex">>,<<"lib/igniter/extension.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"LICENSE">>,
  <<"CHANGELOG.md">>,<<"usage-rules.md">>]}.
{<<"app">>,<<"igniter">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"rewrite">>},
   {<<"app">>,<<"rewrite">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.1 and >= 1.1.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"glob_ex">>},
   {<<"app">>,<<"glob_ex">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.1.7">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"spitfire">>},
   {<<"app">>,<<"spitfire">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.1 and >= 0.1.3">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"sourceror">>},
   {<<"app">>,<<"sourceror">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.4">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"jason">>},
   {<<"app">>,<<"jason">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.4">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"req">>},
   {<<"app">>,<<"req">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.5">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"phx_new">>},
   {<<"app">>,<<"phx_new">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.7">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"owl">>},
   {<<"app">>,<<"owl">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.11">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
