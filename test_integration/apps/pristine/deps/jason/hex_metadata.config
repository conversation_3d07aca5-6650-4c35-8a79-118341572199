{<<"links">>,[{<<"GitHub">>,<<"https://github.com/michalmuskala/jason">>}]}.
{<<"name">>,<<"jason">>}.
{<<"version">>,<<"1.4.4">>}.
{<<"description">>,
 <<"A blazing fast JSON parser and generator in pure Elixir.">>}.
{<<"elixir">>,<<"~> 1.4">>}.
{<<"app">>,<<"jason">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"decimal">>},
   {<<"app">>,<<"decimal">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.0 or ~> 2.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/jason.ex">>,<<"lib/encoder.ex">>,<<"lib/decoder.ex">>,
  <<"lib/ordered_object.ex">>,<<"lib/formatter.ex">>,<<"lib/encode.ex">>,
  <<"lib/codegen.ex">>,<<"lib/helpers.ex">>,<<"lib/sigil.ex">>,
  <<"lib/fragment.ex">>,<<"mix.exs">>,<<"README.md">>,<<"LICENSE">>,
  <<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
