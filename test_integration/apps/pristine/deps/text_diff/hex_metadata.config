{<<"links">>,[{<<"GitHub">>,<<"https://github.com/hrzndhrn/text_diff">>}]}.
{<<"name">>,<<"text_diff">>}.
{<<"version">>,<<"0.1.0">>}.
{<<"description">>,
 <<"TextDiff returns a formatted diff between two strings.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"text_diff">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/text_diff.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
