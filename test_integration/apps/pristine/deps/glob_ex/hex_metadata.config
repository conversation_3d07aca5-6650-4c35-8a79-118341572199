{<<"links">>,[{<<"GitHub">>,<<"https://github.com/hrzndhrn/glob_ex">>}]}.
{<<"name">>,<<"glob_ex">>}.
{<<"version">>,<<"0.1.11">>}.
{<<"description">>,<<"A library for glob expressions.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"glob_ex">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/glob_ex.ex">>,<<"lib/glob_ex">>,
  <<"lib/glob_ex/sigils.ex">>,<<"lib/glob_ex/compiler_error.ex">>,
  <<"lib/glob_ex/compiler.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
