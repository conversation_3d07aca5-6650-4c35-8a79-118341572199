# Changelog

## 0.1.11 - 2024/10/17

+ Fix for matching hidden files with an exact glob from root.

## 0.1.10 - 2024/10/17

+ Shorten `[:double_star, :start]` to `[:double_star]`.
+ Fix for `:exact` matching followed by `:double_star` in `GlobEx.match/2`.

## 0.1.9 - 2024/09/30

+ Add `:input` to `GlobEx.CompileError`.

## 0.1.8 - 2024/07/27

+ Minor refactroings.
+ Fix typos.
+ Add docs.

## 0.1.7 - 2024/05/24

+ Use `~c`.

## 0.1.6 - 2023/10/08

+ Some minor refactroings.

## 0.1.5 - 2023/10/06

+ Some minor refactroings.

## 0.1.4 - 2023/05/13

+ Fix for `GlobEx.ls/1` when the glob starts with `/`.

## 0.1.3 - 2023/04/15

+ Fix for a bug with exact matching components in blog expresion.
+ Refactor path split.

## 0.1.2 - 2023/04/12

+ Fix to handle the root folder.

## 0.1.1 - 2022/11/19

+ Update docs.

## 0.1.0 - 2022/11/19

+ The very first version.
