{<<"links">>,[{<<"GitHub">>,<<"https://github.com/elixir-ecto/ecto_sql">>}]}.
{<<"name">>,<<"ecto_sql">>}.
{<<"version">>,<<"3.13.2">>}.
{<<"description">>,<<"SQL-based adapters for Ecto and database migrations">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"files">>,
 [<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>,
  <<"lib">>,<<"lib/ecto">>,<<"lib/ecto/adapter">>,
  <<"lib/ecto/adapter/migration.ex">>,<<"lib/ecto/adapter/structure.ex">>,
  <<"lib/ecto/adapters">>,<<"lib/ecto/adapters/tds.ex">>,
  <<"lib/ecto/adapters/myxql">>,<<"lib/ecto/adapters/myxql/connection.ex">>,
  <<"lib/ecto/adapters/tds">>,<<"lib/ecto/adapters/tds/types.ex">>,
  <<"lib/ecto/adapters/tds/connection.ex">>,<<"lib/ecto/adapters/sql.ex">>,
  <<"lib/ecto/adapters/postgres">>,
  <<"lib/ecto/adapters/postgres/connection.ex">>,
  <<"lib/ecto/adapters/myxql.ex">>,<<"lib/ecto/adapters/postgres.ex">>,
  <<"lib/ecto/adapters/sql">>,<<"lib/ecto/adapters/sql/stream.ex">>,
  <<"lib/ecto/adapters/sql/sandbox.ex">>,
  <<"lib/ecto/adapters/sql/connection.ex">>,
  <<"lib/ecto/adapters/sql/application.ex">>,<<"lib/ecto/migration.ex">>,
  <<"lib/ecto/migrator.ex">>,<<"lib/ecto/migration">>,
  <<"lib/ecto/migration/runner.ex">>,
  <<"lib/ecto/migration/schema_migration.ex">>,<<"lib/mix">>,
  <<"lib/mix/tasks">>,<<"lib/mix/tasks/ecto.migrations.ex">>,
  <<"lib/mix/tasks/ecto.dump.ex">>,<<"lib/mix/tasks/ecto.rollback.ex">>,
  <<"lib/mix/tasks/ecto.migrate.ex">>,
  <<"lib/mix/tasks/ecto.gen.migration.ex">>,<<"lib/mix/tasks/ecto.load.ex">>,
  <<"lib/mix/ecto_sql.ex">>,<<"integration_test/sql">>,
  <<"integration_test/sql/migrator.exs">>,
  <<"integration_test/sql/query_many.exs">>,
  <<"integration_test/sql/lock.exs">>,
  <<"integration_test/sql/transaction.exs">>,
  <<"integration_test/sql/alter.exs">>,<<"integration_test/sql/stream.exs">>,
  <<"integration_test/sql/sql.exs">>,<<"integration_test/sql/sandbox.exs">>,
  <<"integration_test/sql/subquery.exs">>,
  <<"integration_test/sql/logging.exs">>,
  <<"integration_test/sql/migration.exs">>,<<"integration_test/support">>,
  <<"integration_test/support/file_helpers.exs">>,
  <<"integration_test/support/migration.exs">>,
  <<"integration_test/support/repo.exs">>]}.
{<<"app">>,<<"ecto_sql">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"ecto">>},
   {<<"app">>,<<"ecto">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 3.13.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"telemetry">>},
   {<<"app">>,<<"telemetry">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.4.0 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"db_connection">>},
   {<<"app">>,<<"db_connection">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 2.5 or ~> 2.4.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"postgrex">>},
   {<<"app">>,<<"postgrex">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.19 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"myxql">>},
   {<<"app">>,<<"myxql">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.7">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"tds">>},
   {<<"app">>,<<"tds">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 2.1.1 or ~> 2.2">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
