{<<"links">>,
 [{<<"Changelog">>,<<"https://hexdocs.pm/finch/changelog.html">>},
  {<<"GitHub">>,<<"https://github.com/sneako/finch">>}]}.
{<<"name">>,<<"finch">>}.
{<<"version">>,<<"0.20.0">>}.
{<<"description">>,<<"An HTTP client focused on performance.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"finch">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"mint">>},
   {<<"app">>,<<"mint">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.6.2 or ~> 1.7">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"nimble_pool">>},
   {<<"app">>,<<"nimble_pool">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"nimble_options">>},
   {<<"app">>,<<"nimble_options">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.4 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"telemetry">>},
   {<<"app">>,<<"telemetry">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.4 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"mime">>},
   {<<"app">>,<<"mime">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0 or ~> 2.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/finch.ex">>,<<"lib/finch">>,<<"lib/finch/http1">>,
  <<"lib/finch/http1/conn.ex">>,<<"lib/finch/http1/pool.ex">>,
  <<"lib/finch/http1/pool_metrics.ex">>,<<"lib/finch/telemetry.ex">>,
  <<"lib/finch/response.ex">>,<<"lib/finch/request.ex">>,
  <<"lib/finch/pool_manager.ex">>,<<"lib/finch/error.ex">>,
  <<"lib/finch/http2">>,<<"lib/finch/http2/request_stream.ex">>,
  <<"lib/finch/http2/pool.ex">>,<<"lib/finch/http2/pool_metrics.ex">>,
  <<"lib/finch/ssl.ex">>,<<"lib/finch/pool.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
