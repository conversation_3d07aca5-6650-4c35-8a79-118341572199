{<<"links">>,[{<<"GitHub">>,<<"https://github.com/ericmj/decimal">>}]}.
{<<"name">>,<<"decimal">>}.
{<<"version">>,<<"2.3.0">>}.
{<<"description">>,<<"Arbitrary precision decimal arithmetic.">>}.
{<<"elixir">>,<<"~> 1.8">>}.
{<<"app">>,<<"decimal">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/decimal">>,<<"lib/decimal/error.ex">>,
  <<"lib/decimal/context.ex">>,<<"lib/decimal/macros.ex">>,
  <<"lib/decimal.ex">>,<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE.txt">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
