name: CI

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

permissions:
  contents: read

jobs:
  build:
    name: Build and test
    runs-on: ubuntu-latest

    strategy:
      matrix:
        include:
          - elixir: "1.14"
            otp: "24.3"

          - elixir: "1.15"
            otp: "25.3"

          - elixir: "1.16"
            otp: "26.2"

          - elixir: "1.17"
            otp: "27.3"

          - elixir: "1.18.4-otp-27"
            otp: "28.0"

    steps:
      - uses: actions/checkout@v3
      - name: Set up Elixir
        uses: erlef/setup-beam@main # v1.16.0
        with:
          otp-version: ${{ matrix.otp }}
          elixir-version: ${{ matrix.elixir }}
      - name: Restore dependencies cache
        uses: actions/cache@v3
        with:
          path: deps
          key: ${{ runner.os }}-mix-${{ hashFiles('**/mix.lock') }}
          restore-keys: ${{ runner.os }}-mix-
      - name: Install dependencies
        run: mix deps.get
      - name: Run tests
        run: mix test
