{<<"app">>,<<"telemetry">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,
 <<"Dynamic dispatching library for metrics and instrumentations">>}.
{<<"files">>,
 [<<"CHANGELOG.md">>,<<"LICENSE">>,<<"NOTICE">>,<<"README.md">>,<<"mix.exs">>,
  <<"rebar.config">>,<<"rebar.lock">>,<<"src">>,<<"src/telemetry.app.src">>,
  <<"src/telemetry.erl">>,<<"src/telemetry.hrl">>,<<"src/telemetry_app.erl">>,
  <<"src/telemetry_handler_table.erl">>,<<"src/telemetry_sup.erl">>,
  <<"src/telemetry_test.erl">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/beam-telemetry/telemetry">>}]}.
{<<"name">>,<<"telemetry">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"1.3.0">>}.
