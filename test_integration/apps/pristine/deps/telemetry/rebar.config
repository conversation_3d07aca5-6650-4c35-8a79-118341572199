{erl_opts, [debug_info]}.
{deps, []}.

{profiles, [
    {test, [{erl_opts, [nowarn_export_all]},
            %% create junit xml for circleci
            {ct_opts, [{ct_hooks, [cth_surefire]}]},
            {cover_enabled, true},
            {cover_opts, [verbose]},
            %% convert to data codecov understands
            {plugins, [covertool]},
            {covertool, [{coverdata_files, ["ct.coverdata"]}]}
    ]}
]}.

{shell, [{apps, [telemetry]}]}.

%% take out warnings for unused exported functions
{xref_checks,[undefined_function_calls, undefined_functions, locals_not_used,
              deprecated_function_calls, deprecated_functions]}.

{project_plugins, [rebar3_hex, rebar3_ex_doc]}.

{hex, [
    {doc, #{provider => ex_doc}}
]}.

{ex_doc, [
    {source_url, <<"https://github.com/beam-telemetry/telemetry">>},
    {extras, [<<"README.md">>, <<"CHANGELOG.md">>, <<"LICENSE">>, <<"NOTICE">>]},
    {main, <<"readme">>}
]}.
