{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/elixir-tools/spitfire">>},
  {<<"Sponsor">>,<<"https://github.com/sponsors/mhanberg">>}]}.
{<<"name">>,<<"spitfire">>}.
{<<"version">>,<<"0.2.1">>}.
{<<"description">>,<<"Error resilient parser for Elixir">>}.
{<<"elixir">>,<<"~> 1.15">>}.
{<<"app">>,<<"spitfire">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"files">>,
 [<<"lib">>,<<"lib/spitfire">>,<<"lib/spitfire/while.ex">>,
  <<"lib/spitfire/tracer.ex">>,<<"lib/spitfire/env.ex">>,
  <<"lib/spitfire.ex">>,<<"src">>,<<"src/spitfire_tokenizer.hrl">>,
  <<"src/spitfire_tokenizer.erl">>,<<"src/spitfire.hrl">>,
  <<"src/spitfire_interpolation.erl">>,<<"LICENSE">>,<<"mix.exs">>,
  <<"README.md">>,<<".formatter.exs">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.
