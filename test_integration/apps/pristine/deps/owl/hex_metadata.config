{<<"links">>,[{<<"GitHub">>,<<"https://github.com/fuelen/owl">>}]}.
{<<"name">>,<<"owl">>}.
{<<"version">>,<<"0.12.2">>}.
{<<"description">>,<<"A toolkit for writing command-line user interfaces.">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"app">>,<<"owl">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"ucwidth">>},
   {<<"app">>,<<"ucwidth">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.2">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/owl.ex">>,<<"lib/owl">>,<<"lib/owl/application.ex">>,
  <<"lib/owl/border_style.ex">>,<<"lib/owl/box.ex">>,<<"lib/owl/daemon.ex">>,
  <<"lib/owl/data.ex">>,<<"lib/owl/data">>,<<"lib/owl/data/sequence.ex">>,
  <<"lib/owl/io.ex">>,<<"lib/owl/lines.ex">>,<<"lib/owl/progress_bar.ex">>,
  <<"lib/owl/spinner.ex">>,<<"lib/owl/system.ex">>,<<"lib/owl/system">>,
  <<"lib/owl/system/helpers.ex">>,<<"lib/owl/table.ex">>,<<"lib/owl/tag.ex">>,
  <<"lib/owl/task.ex">>,<<"lib/owl/true_color.ex">>,
  <<"lib/owl/live_screen.ex">>,<<"lib/owl/palette.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE.txt">>]}.
{<<"build_tools">>,[<<"mix">>]}.
