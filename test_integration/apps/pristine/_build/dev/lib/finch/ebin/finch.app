{application,finch,
             [{modules,['<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>.Error',
                        '<PERSON><PERSON><PERSON><PERSON>.HTTP1.Conn','Elixir.Finch.HTTP1.Pool',
                        'Elixir.Finch.HTTP1.Pool.State',
                        'Elixir.Finch.HTTP1.PoolMetrics',
                        'Elixir.Finch.HTTP2.Pool',
                        'Elixir.Finch.HTTP2.PoolMetrics',
                        'Elixir.Finch.HTTP2.RequestStream',
                        'Elixir.Finch.Pool','Elixir.Finch.PoolManager',
                        'Elixir.Finch.Request','Elixir.Finch.Response',
                        'Elixir.Finch.SSL','Elixir.Finch.Telemetry']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,mint,nimble_pool,
                             nimble_options,telemetry,mime]},
              {description,"An HTTP client focused on performance."},
              {registered,[]},
              {vsn,"0.20.0"}]}.
