{application,drops_relation,
             [{modules,['Elixir.Drops.Relation',
                        'Elixir.Drops.Relation.Application',
                        'Elixir.Drops.Relation.Cache',
                        'Elixir.Drops.Relation.Compilation',
                        'Elixir.Drops.Relation.Compilation.Context',
                        'Elixir.Drops.Relation.Compilers.CodeCompiler',
                        'Elixir.Drops.Relation.Compilers.EctoCompiler',
                        'Elixir.Drops.Relation.Compilers.PostgresSchemaCompiler',
                        'Elixir.Drops.Relation.Compilers.SchemaCompiler',
                        'Elixir.Drops.Relation.Compilers.SqliteSchemaCompiler',
                        'Elixir.Drops.Relation.Config',
                        'Elixir.Drops.Relation.Generator',
                        'Elixir.Drops.Relation.Inflector',
                        'Elixir.Drops.Relation.Loadable',
                        'Elixir.Drops.Relation.Loaded',
                        'Elixir.Drops.Relation.Operations.And',
                        'Elixir.Drops.Relation.Operations.Or',
                        'Elixir.Drops.Relation.Plugin',
                        'Elixir.Drops.Relation.Plugin.MacroStruct',
                        'Elixir.Drops.Relation.Plugins.AutoRestrict',
                        'Elixir.Drops.Relation.Plugins.AutoRestrict.SchemaCompiler',
                        'Elixir.Drops.Relation.Plugins.Ecto.Query',
                        'Elixir.Drops.Relation.Plugins.Ecto.Query.Macros.Defquery',
                        'Elixir.Drops.Relation.Plugins.Loadable',
                        'Elixir.Drops.Relation.Plugins.Pagination',
                        'Elixir.Drops.Relation.Plugins.Queryable',
                        'Elixir.Drops.Relation.Plugins.Queryable.InvalidQueryError',
                        'Elixir.Drops.Relation.Plugins.Queryable.Operations.Compiler',
                        'Elixir.Drops.Relation.Plugins.Queryable.Operations.Compiler.Result',
                        'Elixir.Drops.Relation.Plugins.Queryable.Operations.Order.Compiler',
                        'Elixir.Drops.Relation.Plugins.Queryable.Operations.Preload.Compiler',
                        'Elixir.Drops.Relation.Plugins.Queryable.Operations.Restrict.Compiler',
                        'Elixir.Drops.Relation.Plugins.Reading',
                        'Elixir.Drops.Relation.Plugins.Schema',
                        'Elixir.Drops.Relation.Plugins.Schema.Macros.Schema',
                        'Elixir.Drops.Relation.Plugins.Views',
                        'Elixir.Drops.Relation.Plugins.Views.Macros.Derive',
                        'Elixir.Drops.Relation.Plugins.Views.Macros.View',
                        'Elixir.Drops.Relation.Plugins.Writing',
                        'Elixir.Drops.Relation.Query',
                        'Elixir.Drops.Relation.Schema',
                        'Elixir.Drops.Relation.Schema.Field',
                        'Elixir.Drops.Relation.Schema.ForeignKey',
                        'Elixir.Drops.Relation.Schema.Index',
                        'Elixir.Drops.Relation.Schema.Patcher',
                        'Elixir.Drops.Relation.Schema.PrimaryKey',
                        'Elixir.Drops.Relation.Schema.Serializable',
                        'Elixir.Drops.Relation.Schema.Serializable.Dumper',
                        'Elixir.Drops.Relation.Schema.Serializable.Dumper.Any',
                        'Elixir.Drops.Relation.Schema.Serializable.Dumper.List',
                        'Elixir.Drops.Relation.Schema.Serializable.Dumper.Map',
                        'Elixir.Drops.Relation.Schema.Serializable.Dumper.Tuple',
                        'Elixir.Drops.Relation.Schema.Serializable.Loader',
                        'Elixir.Drops.Relation.Schema.Serializable.Loader.Any',
                        'Elixir.Drops.Relation.Schema.Serializable.Loader.List',
                        'Elixir.Drops.Relation.Schema.Serializable.Loader.Map',
                        'Elixir.Drops.SQL.Compiler',
                        'Elixir.Drops.SQL.Compilers.Postgres',
                        'Elixir.Drops.SQL.Compilers.Sqlite',
                        'Elixir.Drops.SQL.Database',
                        'Elixir.Drops.SQL.Database.Column',
                        'Elixir.Drops.SQL.Database.ForeignKey',
                        'Elixir.Drops.SQL.Database.Index',
                        'Elixir.Drops.SQL.Database.PrimaryKey',
                        'Elixir.Drops.SQL.Database.Table',
                        'Elixir.Drops.SQL.Postgres','Elixir.Drops.SQL.Sqlite',
                        'Elixir.Ecto.Queryable.Drops.Relation.Operations.And',
                        'Elixir.Ecto.Queryable.Drops.Relation.Operations.Or',
                        'Elixir.Enumerable.Drops.Relation.Loaded',
                        'Elixir.Enumerable.Drops.Relation.Operations.And',
                        'Elixir.Enumerable.Drops.Relation.Operations.Or',
                        'Elixir.Enumerable.Drops.Relation.Schema',
                        'Elixir.Enumerable.Drops.Relation.Schema.Field',
                        'Elixir.JSON.Encoder.Drops.Relation.Schema',
                        'Elixir.JSON.Encoder.Drops.Relation.Schema.Field',
                        'Elixir.JSON.Encoder.Drops.Relation.Schema.ForeignKey',
                        'Elixir.JSON.Encoder.Drops.Relation.Schema.Index',
                        'Elixir.JSON.Encoder.Drops.Relation.Schema.PrimaryKey',
                        'Elixir.Mix.Tasks.Drops.Relation.GenSchemas',
                        'Elixir.Mix.Tasks.Drops.Relation.Install',
                        'Elixir.Mix.Tasks.Drops.Relation.RefreshCache']},
              {optional_applications,[igniter]},
              {applications,[kernel,stdlib,elixir,logger,nimble_options,
                             drops_inflector,ecto,ecto_sql,igniter]},
              {description,"Provides a convenient query API that wraps Ecto.Schema and delegates to Ecto.Repo functions with automatic schema inference from database tables.\n"},
              {vsn,"0.0.1"},
              {mod,{'Elixir.Drops.Relation.Application',[]}},
              {registered,['Elixir.Drops.Relation.Supervisor']}]}.
