{application,rewrite,
             [{modules,['Elixir.Enumerable.Rewrite','Elixir.Inspect.Rewrite',
                        'Elixir.Inspect.Rewrite.Source',
                        'Elixir.Inspect.Rewrite.Source.Ex','Elixir.Rewrite',
                        'Elixir.Rewrite.Application',
                        'Elixir.Rewrite.DotFormatter',
                        'Elixir.Rewrite.DotFormatterError',
                        'Elixir.Rewrite.Error','Elixir.Rewrite.Filetype',
                        'Elixir.Rewrite.Hook',
                        'Elixir.Rewrite.Hook.DotFormatterUpdater',
                        'Elixir.Rewrite.Source','Elixir.Rewrite.Source.Ex',
                        'Elixir.Rewrite.SourceError',
                        'Elixir.Rewrite.SourceKeyError',
                        'Elixir.Rewrite.UpdateError']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,glob_ex,sourceror,
                             text_diff]},
              {description,"An API for rewriting sources in an Elixir project. Powered by sourceror."},
              {registered,[]},
              {vsn,"1.1.2"},
              {mod,{'Elixir.Rewrite.Application',[]}}]}.
