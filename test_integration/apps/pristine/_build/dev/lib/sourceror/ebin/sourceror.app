{application,sourceror,
             [{modules,['Elixir.Inspect.Sourceror.Zipper','Elixir.Sourceror',
                        'Elixir.Sourceror.Comments',
                        'Elixir.Sourceror.FastZipper',
                        'Elixir.Sourceror.Identifier',
                        'Elixir.Sourceror.LinesCorrector',
                        'Elixir.Sourceror.Patch','Elixir.Sourceror.Range',
                        'Elixir.Sourceror.TraversalState',
                        'Elixir.Sourceror.Utils.TypedStruct',
                        'Elixir.Sourceror.Zipper',
                        'Elixir.Sourceror.Zipper.Inspect']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"sourceror"},
              {registered,[]},
              {vsn,"1.10.0"}]}.
