{application,ecto,
             [{modules,['Elixir.Ecto','Elixir.Ecto.Adapter',
                        'Elixir.Ecto.Adapter.Queryable',
                        'Elixir.Ecto.Adapter.Schema',
                        'Elixir.Ecto.Adapter.Storage',
                        'Elixir.Ecto.Adapter.Transaction',
                        'Elixir.Ecto.Application','Elixir.Ecto.Association',
                        'Elixir.Ecto.Association.BelongsTo',
                        'Elixir.Ecto.Association.Has',
                        'Elixir.Ecto.Association.HasThrough',
                        'Elixir.Ecto.Association.ManyToMany',
                        'Elixir.Ecto.Association.NotLoaded',
                        'Elixir.Ecto.CastError','Elixir.Ecto.ChangeError',
                        'Elixir.Ecto.Changeset',
                        'Elixir.Ecto.Changeset.Relation',
                        'Elixir.Ecto.ConstraintError','Elixir.Ecto.Embedded',
                        'Elixir.Ecto.Enum',
                        'Elixir.Ecto.InvalidChangesetError',
                        'Elixir.Ecto.InvalidURLError',
                        'Elixir.Ecto.MigrationError','Elixir.Ecto.Multi',
                        'Elixir.Ecto.MultiplePrimaryKeyError',
                        'Elixir.Ecto.MultipleResultsError',
                        'Elixir.Ecto.NoPrimaryKeyFieldError',
                        'Elixir.Ecto.NoPrimaryKeyValueError',
                        'Elixir.Ecto.NoResultsError',
                        'Elixir.Ecto.ParameterizedType','Elixir.Ecto.Query',
                        'Elixir.Ecto.Query.API',
                        'Elixir.Ecto.Query.BooleanExpr',
                        'Elixir.Ecto.Query.Builder',
                        'Elixir.Ecto.Query.Builder.CTE',
                        'Elixir.Ecto.Query.Builder.Combination',
                        'Elixir.Ecto.Query.Builder.Distinct',
                        'Elixir.Ecto.Query.Builder.Dynamic',
                        'Elixir.Ecto.Query.Builder.Filter',
                        'Elixir.Ecto.Query.Builder.From',
                        'Elixir.Ecto.Query.Builder.GroupBy',
                        'Elixir.Ecto.Query.Builder.Join',
                        'Elixir.Ecto.Query.Builder.LimitOffset',
                        'Elixir.Ecto.Query.Builder.Lock',
                        'Elixir.Ecto.Query.Builder.OrderBy',
                        'Elixir.Ecto.Query.Builder.Preload',
                        'Elixir.Ecto.Query.Builder.Select',
                        'Elixir.Ecto.Query.Builder.Update',
                        'Elixir.Ecto.Query.Builder.Windows',
                        'Elixir.Ecto.Query.ByExpr',
                        'Elixir.Ecto.Query.CastError',
                        'Elixir.Ecto.Query.CompileError',
                        'Elixir.Ecto.Query.DynamicExpr',
                        'Elixir.Ecto.Query.FromExpr',
                        'Elixir.Ecto.Query.JoinExpr',
                        'Elixir.Ecto.Query.LimitExpr',
                        'Elixir.Ecto.Query.Planner',
                        'Elixir.Ecto.Query.QueryExpr',
                        'Elixir.Ecto.Query.SelectExpr',
                        'Elixir.Ecto.Query.Tagged','Elixir.Ecto.Query.Values',
                        'Elixir.Ecto.Query.WindowAPI',
                        'Elixir.Ecto.Query.WithExpr','Elixir.Ecto.QueryError',
                        'Elixir.Ecto.Queryable','Elixir.Ecto.Queryable.Atom',
                        'Elixir.Ecto.Queryable.BitString',
                        'Elixir.Ecto.Queryable.Ecto.Query',
                        'Elixir.Ecto.Queryable.Ecto.SubQuery',
                        'Elixir.Ecto.Queryable.Tuple','Elixir.Ecto.Repo',
                        'Elixir.Ecto.Repo.Assoc','Elixir.Ecto.Repo.Preloader',
                        'Elixir.Ecto.Repo.Queryable',
                        'Elixir.Ecto.Repo.Registry','Elixir.Ecto.Repo.Schema',
                        'Elixir.Ecto.Repo.Supervisor',
                        'Elixir.Ecto.Repo.Transaction','Elixir.Ecto.Schema',
                        'Elixir.Ecto.Schema.Loader',
                        'Elixir.Ecto.Schema.Metadata',
                        'Elixir.Ecto.StaleEntryError','Elixir.Ecto.SubQuery',
                        'Elixir.Ecto.SubQueryError','Elixir.Ecto.Type',
                        'Elixir.Ecto.UUID',
                        'Elixir.Inspect.Ecto.Association.NotLoaded',
                        'Elixir.Inspect.Ecto.Changeset',
                        'Elixir.Inspect.Ecto.Query',
                        'Elixir.Inspect.Ecto.Query.DynamicExpr',
                        'Elixir.Inspect.Ecto.Schema.Metadata',
                        'Elixir.JSON.Encoder.Ecto.Association.NotLoaded',
                        'Elixir.JSON.Encoder.Ecto.Schema.Metadata',
                        'Elixir.Jason.Encoder.Ecto.Association.NotLoaded',
                        'Elixir.Jason.Encoder.Ecto.Schema.Metadata',
                        'Elixir.Mix.Ecto','Elixir.Mix.Tasks.Ecto',
                        'Elixir.Mix.Tasks.Ecto.Create',
                        'Elixir.Mix.Tasks.Ecto.Drop',
                        'Elixir.Mix.Tasks.Ecto.Gen.Repo']},
              {optional_applications,[jason]},
              {applications,[kernel,stdlib,elixir,logger,crypto,eex,telemetry,
                             decimal,jason]},
              {description,"A toolkit for data mapping and language integrated query for Elixir"},
              {registered,[]},
              {vsn,"3.13.2"},
              {mod,{'Elixir.Ecto.Application',[]}}]}.
